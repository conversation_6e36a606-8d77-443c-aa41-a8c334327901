🎨 Generating color stops for gradientColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in gradientColorStops
gradient-color-picker.js:363 🎨 Generating color stops for radialColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in radialColorStops
gradient-color-picker.js:363 🎨 Generating color stops for gradientColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in gradientColorStops
gradient-color-picker.js:363 🎨 Generating color stops for radialColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in radialColorStops
design-editor.js:2088 🎨 Text color changed: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:2096 🎨 Applying gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:2097 🎨 Selected object index: 0
design-editor.js:2098 🎨 Selected object: {id: 0, type: 'text', text: 'DESIGN', x: 932.75, y: 912.75, …}
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (gradient) ===
design-editor.js:1272 Updating property 'gradient' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: null, newValue: {…}}
design-editor.js:1335 Property 'gradient' updated: {oldValue: null, newValue: {…}, effectiveValue: {…}}
design-editor.js:1578 Forcing redraw...
design-editor.js:3281 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3723 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 42
design-editor.js:3104 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3111 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 42
design-editor.js:3874 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3878 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3979 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4002 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 42
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 42
design-editor.js:1580 Property 'gradient' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (color) ===
design-editor.js:1272 Updating property 'color' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: '#3b82f6', newValue: '#3b82f6'}
design-editor.js:1335 Property 'color' updated: {oldValue: '#3b82f6', newValue: '#3b82f6', effectiveValue: '#3b82f6'}
design-editor.js:1578 Forcing redraw...
design-editor.js:3281 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3723 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 42
design-editor.js:3104 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3111 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 42
design-editor.js:3874 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3878 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3979 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4002 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 42
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 42
design-editor.js:1580 Property 'color' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10631 📚 State saved to history: Change gradient Index: 8 Stack size: 9
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 42, newValue: 41}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 42, newValue: 41, effectiveValue: 41}
design-editor.js:1578 Forcing redraw...
design-editor.js:3281 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3723 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 41
design-editor.js:3104 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3111 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 41
design-editor.js:3874 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3878 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3979 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4002 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 41
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 41
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 41, newValue: 42}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 41, newValue: 42, effectiveValue: 42}
design-editor.js:1578 Forcing redraw...
design-editor.js:3281 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3723 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 42
design-editor.js:3104 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3111 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 42
design-editor.js:3874 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3878 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3979 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4002 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 42
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 42
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 42, newValue: 43}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 42, newValue: 43, effectiveValue: 43}
design-editor.js:1578 Forcing redraw...
design-editor.js:3281 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3723 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 43
design-editor.js:3104 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3111 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 43
design-editor.js:3874 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3878 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3979 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4002 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 43
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 43
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 43, newValue: 44}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 43, newValue: 44, effectiveValue: 44}
design-editor.js:1578 Forcing redraw...
design-editor.js:3281 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3723 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 44
design-editor.js:3104 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3111 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 44
design-editor.js:3874 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3878 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3979 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4002 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 44
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 44
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10631 📚 State saved to history: Change letterSpacing Index: 9 Stack size: 10