=== UPDATE OBJECT PROPERTY DEBUG (effectMode) ===
design-editor.js:1272 Updating property 'effectMode' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 'normal', newValue: 'grid-distort'}
design-editor.js:1335 Property 'effectMode' updated: {oldValue: 'normal', newValue: 'grid-distort', effectiveValue: 'grid-distort'}
design-editor.js:1575 Updated body class for effectMode change
design-editor.js:1578 Forcing redraw...
design-editor.js:5392 🔍 GRID DISTORT CALL [eb4e2j]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:5491 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 248, lastFontSize: 248, hasRelativePoints: 2}
design-editor.js:5534 🔍 GRID DISTORT CALL [eb4e2j]: Using main font: Verdana_bold_normal - SINGLE RENDER
design-editor.js:6377 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 248, fontFamily: 'Verdana', bold: true, italic: false, …}
design-editor.js:6399 Creating text path with letter spacing: 28
design-editor.js:6448 Text dimensions: {measuredWidth: 1139.62890625, measuredHeight: 189, pathWidth: 1191.94140625, pathHeight: 187.57421875, finalWidth: 1139.62890625, …}
design-editor.js:6479 Grid bounds calculation: {gridLeft: -699.51259765625, gridTop: -224.19814453125, gridWidth: 1399.0251953125, gridHeight: 448.3962890625, textWidth: 1139.62890625, …}
design-editor.js:6490 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:6755 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:6776 Grid Distort text rendered with letter spacing: 28
design-editor.js:5544 Grid visibility: true Selected: true
design-editor.js:5546 Drawing grid...
design-editor.js:5903 Drawing grid with 2 rows and 3 columns
design-editor.js:5551 🔍 GRID DISTORT CALL [eb4e2j]: Main font successful - skipping fallback
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 248px Verdana-bold letter spacing: 28
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 248px Verdana-bold letter spacing: 28
design-editor.js:1580 Property 'effectMode' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:8565 Switching to grid-distort mode, initializing grid
design-editor.js:10497 📚 State saved to history: Change effectMode Index: 49 Stack size: 50