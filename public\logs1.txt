=== UPDATE OBJECT PROPERTY DEBUG (effectMode) ===
design-editor.js:1272 Updating property 'effectMode' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 'normal', newValue: 'mesh'}
design-editor.js:1335 Property 'effectMode' updated: {oldValue: 'normal', newValue: 'mesh', effectiveValue: 'mesh'}
design-editor.js:1575 Updated body class for effectMode change
design-editor.js:1578 Forcing redraw...
design-editor.js:7603 [MeshRender] Using object's own mesh handler for: DESIGN
mesh-warp-implementation.js:718 🎨 GRADIENT MASK: Drawing mesh warp text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:694 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:694 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
mesh-warp-implementation.js:825 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for Mesh Warp
decoration-module.js:192 🎨 DECORATION: Using custom bounds for distorted text: {width: 1766.9999694824219, height: 600, isMeshWarp: true}
decoration-module.js:209 🎨 DECORATION: Mesh warp text bounds - width: 1766.9999694824219 height: 600
decoration-module.js:263 🎨 HORIZONTAL LINES: Coverage 100%, expanded height: 600.0px
mesh-warp-implementation.js:866 🎨 DECORATION: Applied decoration pattern for mesh warp text: horizontalLines
design-editor.js:4316 🎨 MASK: Creating mesh warp text mask for decorations
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:4345 🎨 MASK: Created mesh warp text mask
mesh-warp-implementation.js:895 🎨 DECORATION: Applied decoration effects on top of mesh warp gradient text
mesh-warp-implementation.js:899 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp
mesh-warp-implementation.js:927 🎨 GRADIENT MASK: Mesh warp text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:7943 Mesh effect selected, but no active handler found for object: 0
update @ design-editor.js:7943
updateSelectedObjectFromUI @ design-editor.js:1579
effectModeSelect.onchange @ design-editor.js:8594
handleMouseUp_ @ unknownUnderstand this warning
design-editor.js:1580 Property 'effectMode' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:7603 [MeshRender] Using object's own mesh handler for: DESIGN
mesh-warp-implementation.js:718 🎨 GRADIENT MASK: Drawing mesh warp text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:694 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:694 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
mesh-warp-implementation.js:825 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for Mesh Warp
decoration-module.js:192 🎨 DECORATION: Using custom bounds for distorted text: {width: 1766.9999694824219, height: 600, isMeshWarp: true}
decoration-module.js:209 🎨 DECORATION: Mesh warp text bounds - width: 1766.9999694824219 height: 600
decoration-module.js:263 🎨 HORIZONTAL LINES: Coverage 100%, expanded height: 600.0px
mesh-warp-implementation.js:866 🎨 DECORATION: Applied decoration pattern for mesh warp text: horizontalLines
design-editor.js:4316 🎨 MASK: Creating mesh warp text mask for decorations
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:4345 🎨 MASK: Created mesh warp text mask
mesh-warp-implementation.js:895 🎨 DECORATION: Applied decoration effects on top of mesh warp gradient text
mesh-warp-implementation.js:899 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp
mesh-warp-implementation.js:927 🎨 GRADIENT MASK: Mesh warp text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:7943 Mesh effect selected, but no active handler found for object: 0
update @ design-editor.js:7943
effectModeSelect.onchange @ design-editor.js:8657
handleMouseUp_ @ unknownUnderstand this warning
mesh-warp-implementation.js:1187 Using existing mesh warp handler for object: 0
design-editor.js:7603 [MeshRender] Using object's own mesh handler for: DESIGN
mesh-warp-implementation.js:718 🎨 GRADIENT MASK: Drawing mesh warp text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:694 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:694 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
mesh-warp-implementation.js:825 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for Mesh Warp
decoration-module.js:192 🎨 DECORATION: Using custom bounds for distorted text: {width: 1766.9999694824219, height: 600, isMeshWarp: true}
decoration-module.js:209 🎨 DECORATION: Mesh warp text bounds - width: 1766.9999694824219 height: 600
decoration-module.js:263 🎨 HORIZONTAL LINES: Coverage 100%, expanded height: 600.0px
mesh-warp-implementation.js:866 🎨 DECORATION: Applied decoration pattern for mesh warp text: horizontalLines
design-editor.js:4316 🎨 MASK: Creating mesh warp text mask for decorations
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
mesh-warp-implementation.js:694 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp text (no gradient)
design-editor.js:4345 🎨 MASK: Created mesh warp text mask
mesh-warp-implementation.js:895 🎨 DECORATION: Applied decoration effects on top of mesh warp gradient text
mesh-warp-implementation.js:899 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Mesh Warp
mesh-warp-implementation.js:927 🎨 GRADIENT MASK: Mesh warp text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 0
design-editor.js:10532 📚 State saved to history: Change effectMode Index: 40 Stack size: 41