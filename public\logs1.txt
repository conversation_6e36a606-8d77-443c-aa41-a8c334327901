=== UPDATE OBJECT PROPERTY DEBUG (effectMode) ===
design-editor.js:1272 Updating property 'effectMode' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 'normal', newValue: 'grid-distort'}
design-editor.js:1335 Property 'effectMode' updated: {oldValue: 'normal', newValue: 'grid-distort', effectiveValue: 'grid-distort'}
design-editor.js:1575 Updated body class for effectMode change
design-editor.js:1578 Forcing redraw...
design-editor.js:5647 🔍 GRID DISTORT CALL [40a0v4]: drawGridDistortObject called for text: "DESIGN"
design-editor.js:5746 drawGridDistortObject check: {currentText: 'DESIGN', lastText: 'DESIGN', currentFontSize: 200, lastFontSize: 200, hasRelativePoints: 2}
design-editor.js:5789 🔍 GRID DISTORT CALL [40a0v4]: Using main font: Poppins_bold_normal - SINGLE RENDER
design-editor.js:6632 Drawing grid distorted text with style: {text: 'DESIGN', fontSize: 200, fontFamily: 'Poppins', bold: true, italic: false, …}
design-editor.js:6654 Creating text path with letter spacing: 22
design-editor.js:6703 Text dimensions: {measuredWidth: 857.7999877929688, measuredHeight: 140, pathWidth: 785.8000000000001, pathHeight: 142.60000000000002, finalWidth: 857.7999877929688, …}
design-editor.js:6734 Grid bounds calculation: {gridLeft: -553.1889938354492, gridTop: -194.28899993896485, gridWidth: 1106.3779876708984, gridHeight: 388.5779998779297, textWidth: 857.7999877929688, …}
design-editor.js:6745 🎨 Grid Distort: Set fillStyle to solid color: #3b82f6
design-editor.js:7150 === PERSPECTIVE SHADOW START (roy2we) ===
design-editor.js:7151 [roy2we] Text object: DESIGN
design-editor.js:7304 [roy2we] Path-based perspective shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:7358 [roy2we] Number of path shadow steps: 50
design-editor.js:7376 [roy2we] Path shadow step 1/50: scale=0.810
design-editor.js:7376 [roy2we] Path shadow step 5/50: scale=0.825
design-editor.js:7376 [roy2we] Path shadow step 10/50: scale=0.845
design-editor.js:7376 [roy2we] Path shadow step 15/50: scale=0.864
design-editor.js:7376 [roy2we] Path shadow step 20/50: scale=0.884
design-editor.js:7376 [roy2we] Path shadow step 25/50: scale=0.903
design-editor.js:7376 [roy2we] Path shadow step 30/50: scale=0.922
design-editor.js:7376 [roy2we] Path shadow step 35/50: scale=0.942
design-editor.js:7376 [roy2we] Path shadow step 40/50: scale=0.961
design-editor.js:7376 [roy2we] Path shadow step 45/50: scale=0.981
design-editor.js:7376 [roy2we] Path shadow step 50/50: scale=1.000
design-editor.js:7406 [roy2we] === PERSPECTIVE SHADOW PATH END ===
design-editor.js:7010 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)
design-editor.js:7012 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)
design-editor.js:4605 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system
design-editor.js:4606 🔍 FRONT OUTLINE #1 DETAILS: {outlineColor: '#d1d5db', outlineOpacity: 1, outlineWidth: 3, offsetX: -3, offsetY: -3, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 22
design-editor.js:4786 🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)
design-editor.js:7031 Grid Distort text rendered with letter spacing: 22
design-editor.js:5799 Grid visibility: true Selected: true
design-editor.js:5801 Drawing grid...
design-editor.js:6158 Drawing grid with 2 rows and 3 columns
design-editor.js:5806 🔍 GRID DISTORT CALL [40a0v4]: Main font successful - skipping fallback
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 22
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 22
design-editor.js:1580 Property 'effectMode' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:8855 Switching to grid-distort mode, initializing grid
design-editor.js:10787 📚 State saved to history: Change effectMode Index: 31 Stack size: 32