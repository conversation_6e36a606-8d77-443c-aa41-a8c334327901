🎨 Generating color stops for gradientColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in gradientColorStops
gradient-color-picker.js:363 🎨 Generating color stops for radialColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in radialColorStops
gradient-color-picker.js:363 🎨 Generating color stops for gradientColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in gradientColorStops
gradient-color-picker.js:363 🎨 Generating color stops for radialColorStops: (2) [{…}, {…}]
gradient-color-picker.js:379 🎨 Generated 2 color stops in radialColorStops
design-editor.js:2088 🎨 Text color changed: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:2096 🎨 Applying gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:2097 🎨 Selected object index: 0
design-editor.js:2098 🎨 Selected object: {id: 0, type: 'text', text: 'DESIGN', x: 1054, y: 856.5, …}
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (gradient) ===
design-editor.js:1272 Updating property 'gradient' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: null, newValue: {…}}
design-editor.js:1335 Property 'gradient' updated: {oldValue: null, newValue: {…}, effectiveValue: {…}}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: false
design-editor.js:7980 🎨 MAIN DRAW: Calling drawNormalOrSkewObject for: DESIGN
design-editor.js:3283 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3727 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 0
design-editor.js:3106 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3113 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 0
design-editor.js:3878 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3886 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3983 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4006 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 0
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 0
design-editor.js:1580 Property 'gradient' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10707 📚 State saved to history: Change gradient Index: 3 Stack size: 4
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 0, newValue: 7}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 0, newValue: 7, effectiveValue: 7}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: true
design-editor.js:7959 🎨 SKIP DUPLICATE: Object already rendered by gradient mask system: DESIGN
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 7
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 7
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 7, newValue: 8}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 7, newValue: 8, effectiveValue: 8}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: false
design-editor.js:7980 🎨 MAIN DRAW: Calling drawNormalOrSkewObject for: DESIGN
design-editor.js:3283 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3727 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 8
design-editor.js:3106 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3113 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 8
design-editor.js:3878 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3886 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3983 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4006 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 8
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 8
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 8, newValue: 9}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 8, newValue: 9, effectiveValue: 9}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: true
design-editor.js:7959 🎨 SKIP DUPLICATE: Object already rendered by gradient mask system: DESIGN
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 9
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 9
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 9, newValue: 10}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 9, newValue: 10, effectiveValue: 10}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: false
design-editor.js:7980 🎨 MAIN DRAW: Calling drawNormalOrSkewObject for: DESIGN
design-editor.js:3283 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3727 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 10
design-editor.js:3106 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3113 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 10
design-editor.js:3878 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3886 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3983 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4006 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 10
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 10
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 10, newValue: 11}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 10, newValue: 11, effectiveValue: 11}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: true
design-editor.js:7959 🎨 SKIP DUPLICATE: Object already rendered by gradient mask system: DESIGN
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 11
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 11
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 11, newValue: 12}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 11, newValue: 12, effectiveValue: 12}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: false
design-editor.js:7980 🎨 MAIN DRAW: Calling drawNormalOrSkewObject for: DESIGN
design-editor.js:3283 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3727 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 12
design-editor.js:3106 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3113 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 12
design-editor.js:3878 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3886 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3983 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4006 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 12
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 12
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 12, newValue: 13}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 12, newValue: 13, effectiveValue: 13}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: true
design-editor.js:7959 🎨 SKIP DUPLICATE: Object already rendered by gradient mask system: DESIGN
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 13
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 13
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10707 📚 State saved to history: Change letterSpacing Index: 4 Stack size: 5
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 13, newValue: 11}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 13, newValue: 11, effectiveValue: 11}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: false
design-editor.js:7980 🎨 MAIN DRAW: Calling drawNormalOrSkewObject for: DESIGN
design-editor.js:3283 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3727 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 11
design-editor.js:3106 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3113 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 11
design-editor.js:3878 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3886 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3983 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4006 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 11
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 11
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 11, newValue: 10}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 11, newValue: 10, effectiveValue: 10}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: true
design-editor.js:7959 🎨 SKIP DUPLICATE: Object already rendered by gradient mask system: DESIGN
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 10
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 10
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 10, newValue: 9}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 10, newValue: 9, effectiveValue: 9}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: false
design-editor.js:7980 🎨 MAIN DRAW: Calling drawNormalOrSkewObject for: DESIGN
design-editor.js:3283 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3727 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 9
design-editor.js:3106 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3113 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 9
design-editor.js:3878 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3886 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3983 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4006 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 9
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 9
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 9, newValue: 8}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 9, newValue: 8, effectiveValue: 8}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: true
design-editor.js:7959 🎨 SKIP DUPLICATE: Object already rendered by gradient mask system: DESIGN
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 8
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 8
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10707 📚 State saved to history: Change letterSpacing Index: 5 Stack size: 6
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 8, newValue: 9}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 8, newValue: 9, effectiveValue: 9}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: false
design-editor.js:7980 🎨 MAIN DRAW: Calling drawNormalOrSkewObject for: DESIGN
design-editor.js:3283 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3727 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 9
design-editor.js:3106 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3113 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 9
design-editor.js:3878 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3886 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3983 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4006 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 9
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 9
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 9, newValue: 10}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 9, newValue: 10, effectiveValue: 10}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: true
design-editor.js:7959 🎨 SKIP DUPLICATE: Object already rendered by gradient mask system: DESIGN
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 10
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 10
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 10, newValue: 11}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 10, newValue: 11, effectiveValue: 11}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: false
design-editor.js:7980 🎨 MAIN DRAW: Calling drawNormalOrSkewObject for: DESIGN
design-editor.js:3283 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3727 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 11
design-editor.js:3106 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3113 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 11
design-editor.js:3878 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3886 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3983 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4006 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 11
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 11
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10707 📚 State saved to history: Change letterSpacing Index: 6 Stack size: 7
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 11, newValue: 12}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 11, newValue: 12, effectiveValue: 12}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: true
design-editor.js:7959 🎨 SKIP DUPLICATE: Object already rendered by gradient mask system: DESIGN
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 12
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 12
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 12, newValue: 13}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 12, newValue: 13, effectiveValue: 13}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: false
design-editor.js:7980 🎨 MAIN DRAW: Calling drawNormalOrSkewObject for: DESIGN
design-editor.js:3283 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3727 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 13
design-editor.js:3106 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3113 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 13
design-editor.js:3878 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3886 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3983 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4006 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 13
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 13
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10707 📚 State saved to history: Change letterSpacing Index: 7 Stack size: 8
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 13, newValue: 14}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 13, newValue: 14, effectiveValue: 14}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: true
design-editor.js:7959 🎨 SKIP DUPLICATE: Object already rendered by gradient mask system: DESIGN
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 14
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 14
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 14, newValue: 15}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 14, newValue: 15, effectiveValue: 15}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: false
design-editor.js:7980 🎨 MAIN DRAW: Calling drawNormalOrSkewObject for: DESIGN
design-editor.js:3283 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3727 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 15
design-editor.js:3106 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3113 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 15
design-editor.js:3878 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3886 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3983 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4006 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 15
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 15
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 15, newValue: 16}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 15, newValue: 16, effectiveValue: 16}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: true
design-editor.js:7959 🎨 SKIP DUPLICATE: Object already rendered by gradient mask system: DESIGN
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 16
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 16
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10707 📚 State saved to history: Change letterSpacing Index: 8 Stack size: 9
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 16, newValue: 14}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 16, newValue: 14, effectiveValue: 14}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: false
design-editor.js:7980 🎨 MAIN DRAW: Calling drawNormalOrSkewObject for: DESIGN
design-editor.js:3283 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3727 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 14
design-editor.js:3106 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3113 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 14
design-editor.js:3878 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3886 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3983 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4006 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 14
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 14
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 14, newValue: 13}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 14, newValue: 13, effectiveValue: 13}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: true
design-editor.js:7959 🎨 SKIP DUPLICATE: Object already rendered by gradient mask system: DESIGN
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 13
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 13
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 13, newValue: 14}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 13, newValue: 14, effectiveValue: 14}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: false
design-editor.js:7980 🎨 MAIN DRAW: Calling drawNormalOrSkewObject for: DESIGN
design-editor.js:3283 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3727 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 14
design-editor.js:3106 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3113 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 14
design-editor.js:3878 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3886 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3983 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4006 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 14
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 14
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10707 📚 State saved to history: Change letterSpacing Index: 9 Stack size: 10
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 14, newValue: 16}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 14, newValue: 16, effectiveValue: 16}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: true
design-editor.js:7959 🎨 SKIP DUPLICATE: Object already rendered by gradient mask system: DESIGN
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 16
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 16
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 16, newValue: 17}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 16, newValue: 17, effectiveValue: 17}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: false
design-editor.js:7980 🎨 MAIN DRAW: Calling drawNormalOrSkewObject for: DESIGN
design-editor.js:3283 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3727 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 17
design-editor.js:3106 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3113 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 17
design-editor.js:3878 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3886 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3983 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4006 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 17
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 17
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10707 📚 State saved to history: Change letterSpacing Index: 10 Stack size: 11
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 17, newValue: 16}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 17, newValue: 16, effectiveValue: 16}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: true
design-editor.js:7959 🎨 SKIP DUPLICATE: Object already rendered by gradient mask system: DESIGN
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 16
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 16
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 16, newValue: 15}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 16, newValue: 15, effectiveValue: 15}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: false
design-editor.js:7980 🎨 MAIN DRAW: Calling drawNormalOrSkewObject for: DESIGN
design-editor.js:3283 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3727 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 15
design-editor.js:3106 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3113 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 15
design-editor.js:3878 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3886 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3983 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4006 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 15
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 15
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 15, newValue: 14}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 15, newValue: 14, effectiveValue: 14}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: true
design-editor.js:7959 🎨 SKIP DUPLICATE: Object already rendered by gradient mask system: DESIGN
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 14
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 14
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 14, newValue: 13}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 14, newValue: 13, effectiveValue: 13}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: false
design-editor.js:7980 🎨 MAIN DRAW: Calling drawNormalOrSkewObject for: DESIGN
design-editor.js:3283 🎨 GRADIENT MASK: Using gradient mask system for normal text with gradients
design-editor.js:3727 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 13
design-editor.js:3106 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3113 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 13
design-editor.js:3878 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3882 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3886 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3983 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:4006 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 13
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 13
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 13, newValue: 12}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 13, newValue: 12, effectiveValue: 12}
design-editor.js:1578 Forcing redraw...
design-editor.js:7955 🎨 MAIN DRAW: drawTextObject called for: DESIGN effect: normal hasBeenRendered: true
design-editor.js:7959 🎨 SKIP DUPLICATE: Object already rendered by gradient mask system: DESIGN
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 12
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Verdana-bold letter spacing: 12
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10707 📚 State saved to history: Change letterSpacing Index: 11 Stack size: 12