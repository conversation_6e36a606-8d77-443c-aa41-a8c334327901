Selection] Selected object: image effectMode: undefined hasMeshHandler: false
design-editor.js:8195 [Selection] Selected non-text object, keeping mesh handlers active for text objects
design-editor.js:786 🔍 updateUIFromSelectedObject called
design-editor.js:787 🔍 selectedObjectIndex: 1
design-editor.js:788 🔍 canvasObjects length: 2
design-editor.js:791 🔍 selectedObject: {id: 1, type: 'image', image: img, x: 843.6559734627251, y: 473.5865178970956, …}
design-editor.js:7610 Mesh effect selected, but no handler found for object: 0
drawTextObject @ design-editor.js:7610
(anonymous) @ design-editor.js:7875
update @ design-editor.js:7874
handleMouseDown @ design-editor.js:8222Understand this warning
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #1f626f
design-editor.js:680 🎨 Set default fillStyle for normal text: #1f626f
design-editor.js:695 Setting text context with font: 262px Arial letter spacing: 0
design-editor.js:2929 Applying perspective shadow in renderStyledObjectToOffscreen
design-editor.js:6895 === PERSPECTIVE SHADOW START (mpq7ci) ===
design-editor.js:6896 [mpq7ci] Text object: caserola
design-editor.js:6927 [mpq7ci] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 5, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #1f626f
design-editor.js:680 🎨 Set default fillStyle for normal text: #1f626f
design-editor.js:695 Setting text context with font: 262px Arial letter spacing: 0
design-editor.js:6960 [mpq7ci] Number of shadow steps: 50
design-editor.js:6980 [mpq7ci] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.836
design-editor.js:6994 [mpq7ci] Shadow position: (2041.7, 2071.7), scale: 0.836
design-editor.js:6980 [mpq7ci] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.850
design-editor.js:6994 [mpq7ci] Shadow position: (2042.2, 2069.7), scale: 0.850
design-editor.js:6980 [mpq7ci] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.866
design-editor.js:6994 [mpq7ci] Shadow position: (2042.8, 2067.3), scale: 0.866
design-editor.js:6980 [mpq7ci] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.883
design-editor.js:6994 [mpq7ci] Shadow position: (2043.5, 2064.9), scale: 0.883
design-editor.js:6980 [mpq7ci] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.900
design-editor.js:6994 [mpq7ci] Shadow position: (2044.1, 2062.5), scale: 0.900
design-editor.js:6980 [mpq7ci] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.916
design-editor.js:6994 [mpq7ci] Shadow position: (2044.8, 2060.1), scale: 0.916
design-editor.js:6980 [mpq7ci] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.933
design-editor.js:6994 [mpq7ci] Shadow position: (2045.4, 2057.7), scale: 0.933
design-editor.js:6980 [mpq7ci] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.950
design-editor.js:6994 [mpq7ci] Shadow position: (2046.1, 2055.2), scale: 0.950
design-editor.js:6980 [mpq7ci] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.967
design-editor.js:6994 [mpq7ci] Shadow position: (2046.7, 2052.8), scale: 0.967
design-editor.js:6980 [mpq7ci] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.983
design-editor.js:6994 [mpq7ci] Shadow position: (2047.4, 2050.4), scale: 0.983
design-editor.js:6980 [mpq7ci] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:6994 [mpq7ci] Shadow position: (2048.0, 2048.0), scale: 1.000
design-editor.js:7027 [mpq7ci] === PERSPECTIVE SHADOW END ===
design-editor.js:3077 🎨 STROKE DEBUG - Original width: 5 Effective width: 40
design-editor.js:3078 🎨 STROKE DEBUG - Font used: 262px Arial
design-editor.js:3104 🎨 About to draw text with fillStyle: string #1f626f
design-editor.js:3111 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:3429 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for normal text (non-gradient)
design-editor.js:4597 🎨 NORMAL FRONT OUTLINE: Drawing normal text front outline
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #1f626f
design-editor.js:680 🎨 Set default fillStyle for normal text: #1f626f
design-editor.js:695 Setting text context with font: 262px Arial letter spacing: 0
design-editor.js:4619 🎨 NORMAL FRONT OUTLINE: Drew normal text front outline
design-editor.js:10514 📚 State saved to history: Move Image Index: 49 Stack size: 50