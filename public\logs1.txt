🎨 Generating color stops for gradientColorStops: (2) [{…}, {…}]
gradient-color-picker.js:366 🎨 Generated 2 color stops in gradientColorStops
gradient-color-picker.js:350 🎨 Generating color stops for radialColorStops: (2) [{…}, {…}]
gradient-color-picker.js:366 🎨 Generated 2 color stops in radialColorStops
gradient-color-picker.js:350 🎨 Generating color stops for gradientColorStops: (2) [{…}, {…}]
gradient-color-picker.js:366 🎨 Generated 2 color stops in gradientColorStops
gradient-color-picker.js:350 🎨 Generating color stops for radialColorStops: (2) [{…}, {…}]
gradient-color-picker.js:366 🎨 Generated 2 color stops in radialColorStops
design-editor.js:2088 🎨 Text color changed: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:2096 🎨 Applying gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:2097 🎨 Selected object index: 0
design-editor.js:2098 🎨 Selected object: {id: 0, type: 'text', text: 'DESIGN', x: 1039, y: 840.25, …}
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (gradient) ===
design-editor.js:1272 Updating property 'gradient' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: null, newValue: {…}}
design-editor.js:1335 Property 'gradient' updated: {oldValue: null, newValue: {…}, effectiveValue: {…}}
design-editor.js:1578 Forcing redraw...
design-editor.js:3722 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:3104 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3111 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:3846 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3850 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3854 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3923 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:3946 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:1580 Property 'gradient' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (color) ===
design-editor.js:1272 Updating property 'color' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: '#3b82f6', newValue: '#3b82f6'}
design-editor.js:1335 Property 'color' updated: {oldValue: '#3b82f6', newValue: '#3b82f6', effectiveValue: '#3b82f6'}
design-editor.js:1578 Forcing redraw...
design-editor.js:3722 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:3104 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3111 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:3846 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3850 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3854 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3923 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:3946 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:1580 Property 'color' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10532 📚 State saved to history: Change gradient Index: 22 Stack size: 23
gradient-color-picker.js:350 🎨 Generating color stops for gradientColorStops: (2) [{…}, {…}]
gradient-color-picker.js:366 🎨 Generated 2 color stops in gradientColorStops
gradient-color-picker.js:350 🎨 Generating color stops for radialColorStops: (2) [{…}, {…}]
gradient-color-picker.js:366 🎨 Generated 2 color stops in radialColorStops
design-editor.js:2088 🎨 Text color changed: {type: 'solid', value: '#3b82f6'}
design-editor.js:2090 🎨 Applying solid color: #3b82f6
design-editor.js:2091 🎨 Selected object index: 0
design-editor.js:2092 🎨 Selected object: {id: 0, type: 'text', text: 'DESIGN', x: 1039, y: 840.25, …}
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (color) ===
design-editor.js:1272 Updating property 'color' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: '#3b82f6', newValue: '#3b82f6'}
design-editor.js:1335 Property 'color' updated: {oldValue: '#3b82f6', newValue: '#3b82f6', effectiveValue: '#3b82f6'}
design-editor.js:1578 Forcing redraw...
design-editor.js:3722 🎨 GRADIENT MASK: Drawing normal text with gradient mask
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:3104 🎨 About to draw text with fillStyle: string #3b82f6
design-editor.js:3111 🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'solid'}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:3846 🔍 RENDER ORDER: Step 5 - Drawing shadow effects for normal text
design-editor.js:3850 🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text
design-editor.js:3854 🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text
design-editor.js:3923 🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for normal text
design-editor.js:3946 🎨 GRADIENT MASK: Normal text with gradient mask complete
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: {type: 'linear', value: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)', gradient: {…}}
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:1580 Property 'color' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (gradient) ===
design-editor.js:1272 Updating property 'gradient' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: {…}, newValue: null}
design-editor.js:1335 Property 'gradient' updated: {oldValue: {…}, newValue: null, effectiveValue: null}
design-editor.js:1578 Forcing redraw...
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:3264 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Impact letter spacing: 82
design-editor.js:1580 Property 'gradient' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10532 📚 State saved to history: Change color Index: 23 Stack size: 24