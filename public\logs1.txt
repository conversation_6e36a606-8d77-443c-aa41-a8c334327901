=== SHADOW MODE CHANGE DEBUG ===
design-editor.js:8864 Shadow mode changed to: perspectiveShadow
design-editor.js:8873 Selected object before update: {id: 0, text: 'DESIGN', shadowMode: 'noShadow', blockShadowPerspective: false, blockShadowPerspectiveIntensity: 50}
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (shadowMode) ===
design-editor.js:1272 Updating property 'shadowMode' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 'noShadow', newValue: 'perspectiveShadow'}
design-editor.js:1335 Property 'shadowMode' updated: {oldValue: 'noShadow', newValue: 'perspectiveShadow', effectiveValue: 'perspectiveShadow'}
design-editor.js:1530 Shadow mode set to: perspectiveShadow
design-editor.js:1533 🔍 FRONT OUTLINE DEBUG: Current values before initialization: {shadowMode: 'perspectiveShadow', d3dSecondaryWidth: 4, d3dSecondaryColor: '#00FF00', perspectiveShadowOutlineWidth: 3, perspectiveShadowOutlineColor: '#d1d5db'}
design-editor.js:1563 🔍 FRONT OUTLINE: Force initialized perspective shadow front outline properties: {perspectiveShadowOutlineWidth: 3, perspectiveShadowOutlineColor: '#d1d5db', perspectiveShadowOutlineOpacity: 100, perspectiveShadowOutlineOffsetX: 2, perspectiveShadowOutlineOffsetY: -3}
design-editor.js:1575 Updated body class for shadowMode change
design-editor.js:1578 Forcing redraw...
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7094 === PERSPECTIVE SHADOW START (bw1huv) ===
design-editor.js:7095 [bw1huv] Text object: D
design-editor.js:7126 [bw1huv] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7159 [bw1huv] Number of shadow steps: 50
design-editor.js:7179 [bw1huv] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [bw1huv] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [bw1huv] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [bw1huv] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [bw1huv] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [bw1huv] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [bw1huv] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [bw1huv] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [bw1huv] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [bw1huv] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [bw1huv] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [bw1huv] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [bw1huv] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [bw1huv] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [bw1huv] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [bw1huv] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [bw1huv] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [bw1huv] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [bw1huv] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [bw1huv] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [bw1huv] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [bw1huv] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [bw1huv] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7094 === PERSPECTIVE SHADOW START (140nvy) ===
design-editor.js:7095 [140nvy] Text object: E
design-editor.js:7126 [140nvy] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7159 [140nvy] Number of shadow steps: 50
design-editor.js:7179 [140nvy] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [140nvy] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [140nvy] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [140nvy] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [140nvy] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [140nvy] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [140nvy] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [140nvy] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [140nvy] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [140nvy] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [140nvy] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [140nvy] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [140nvy] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [140nvy] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [140nvy] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [140nvy] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [140nvy] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [140nvy] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [140nvy] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [140nvy] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [140nvy] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [140nvy] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [140nvy] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7094 === PERSPECTIVE SHADOW START (epfj4q) ===
design-editor.js:7095 [epfj4q] Text object: S
design-editor.js:7126 [epfj4q] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7159 [epfj4q] Number of shadow steps: 50
design-editor.js:7179 [epfj4q] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [epfj4q] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [epfj4q] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [epfj4q] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [epfj4q] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [epfj4q] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [epfj4q] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [epfj4q] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [epfj4q] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [epfj4q] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [epfj4q] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [epfj4q] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [epfj4q] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [epfj4q] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [epfj4q] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [epfj4q] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [epfj4q] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [epfj4q] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [epfj4q] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [epfj4q] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [epfj4q] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [epfj4q] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [epfj4q] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7094 === PERSPECTIVE SHADOW START (hpm3fk) ===
design-editor.js:7095 [hpm3fk] Text object: I
design-editor.js:7126 [hpm3fk] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7159 [hpm3fk] Number of shadow steps: 50
design-editor.js:7179 [hpm3fk] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [hpm3fk] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [hpm3fk] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [hpm3fk] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [hpm3fk] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [hpm3fk] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [hpm3fk] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [hpm3fk] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [hpm3fk] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [hpm3fk] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [hpm3fk] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [hpm3fk] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [hpm3fk] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [hpm3fk] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [hpm3fk] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [hpm3fk] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [hpm3fk] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [hpm3fk] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [hpm3fk] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [hpm3fk] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [hpm3fk] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [hpm3fk] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [hpm3fk] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7094 === PERSPECTIVE SHADOW START (xgda1y) ===
design-editor.js:7095 [xgda1y] Text object: G
design-editor.js:7126 [xgda1y] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7159 [xgda1y] Number of shadow steps: 50
design-editor.js:7179 [xgda1y] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [xgda1y] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [xgda1y] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [xgda1y] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [xgda1y] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [xgda1y] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [xgda1y] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [xgda1y] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [xgda1y] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [xgda1y] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [xgda1y] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [xgda1y] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [xgda1y] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [xgda1y] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [xgda1y] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [xgda1y] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [xgda1y] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [xgda1y] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [xgda1y] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [xgda1y] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [xgda1y] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [xgda1y] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [xgda1y] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7094 === PERSPECTIVE SHADOW START (7rnb3k) ===
design-editor.js:7095 [7rnb3k] Text object: N
design-editor.js:7126 [7rnb3k] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7159 [7rnb3k] Number of shadow steps: 50
design-editor.js:7179 [7rnb3k] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [7rnb3k] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [7rnb3k] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [7rnb3k] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [7rnb3k] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [7rnb3k] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [7rnb3k] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [7rnb3k] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [7rnb3k] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [7rnb3k] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [7rnb3k] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [7rnb3k] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [7rnb3k] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [7rnb3k] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [7rnb3k] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [7rnb3k] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [7rnb3k] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [7rnb3k] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [7rnb3k] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [7rnb3k] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [7rnb3k] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [7rnb3k] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [7rnb3k] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:3526 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for normal text (non-gradient)
design-editor.js:4796 🎨 NORMAL FRONT OUTLINE: Drawing normal text front outline
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:4818 🎨 NORMAL FRONT OUTLINE: Drew normal text front outline
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:1580 Property 'shadowMode' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:8883 Selected object after update: {id: 0, text: 'DESIGN', shadowMode: 'perspectiveShadow', blockShadowPerspective: false, blockShadowPerspectiveIntensity: 50}
design-editor.js:8894 Perspective toggle disabled: true
design-editor.js:8938 Showing perspective-shadow-param controls
design-editor.js:8961 Forcing redraw...
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7094 === PERSPECTIVE SHADOW START (y1c9ch) ===
design-editor.js:7095 [y1c9ch] Text object: D
design-editor.js:7126 [y1c9ch] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7159 [y1c9ch] Number of shadow steps: 50
design-editor.js:7179 [y1c9ch] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [y1c9ch] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [y1c9ch] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [y1c9ch] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [y1c9ch] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [y1c9ch] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [y1c9ch] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [y1c9ch] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [y1c9ch] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [y1c9ch] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [y1c9ch] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [y1c9ch] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [y1c9ch] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [y1c9ch] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [y1c9ch] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [y1c9ch] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [y1c9ch] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [y1c9ch] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [y1c9ch] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [y1c9ch] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [y1c9ch] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [y1c9ch] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [y1c9ch] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7094 === PERSPECTIVE SHADOW START (z7qpxz) ===
design-editor.js:7095 [z7qpxz] Text object: E
design-editor.js:7126 [z7qpxz] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7159 [z7qpxz] Number of shadow steps: 50
design-editor.js:7179 [z7qpxz] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [z7qpxz] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [z7qpxz] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [z7qpxz] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [z7qpxz] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [z7qpxz] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [z7qpxz] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [z7qpxz] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [z7qpxz] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [z7qpxz] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [z7qpxz] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [z7qpxz] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [z7qpxz] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [z7qpxz] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [z7qpxz] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [z7qpxz] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [z7qpxz] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [z7qpxz] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [z7qpxz] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [z7qpxz] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [z7qpxz] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [z7qpxz] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [z7qpxz] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7094 === PERSPECTIVE SHADOW START (2uqhe6) ===
design-editor.js:7095 [2uqhe6] Text object: S
design-editor.js:7126 [2uqhe6] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7159 [2uqhe6] Number of shadow steps: 50
design-editor.js:7179 [2uqhe6] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [2uqhe6] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [2uqhe6] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [2uqhe6] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [2uqhe6] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [2uqhe6] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [2uqhe6] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [2uqhe6] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [2uqhe6] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [2uqhe6] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [2uqhe6] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [2uqhe6] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [2uqhe6] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [2uqhe6] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [2uqhe6] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [2uqhe6] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [2uqhe6] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [2uqhe6] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [2uqhe6] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [2uqhe6] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [2uqhe6] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [2uqhe6] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [2uqhe6] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7094 === PERSPECTIVE SHADOW START (tazt5x) ===
design-editor.js:7095 [tazt5x] Text object: I
design-editor.js:7126 [tazt5x] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7159 [tazt5x] Number of shadow steps: 50
design-editor.js:7179 [tazt5x] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [tazt5x] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [tazt5x] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [tazt5x] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [tazt5x] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [tazt5x] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [tazt5x] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [tazt5x] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [tazt5x] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [tazt5x] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [tazt5x] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [tazt5x] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [tazt5x] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [tazt5x] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [tazt5x] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [tazt5x] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [tazt5x] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [tazt5x] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [tazt5x] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [tazt5x] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [tazt5x] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [tazt5x] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [tazt5x] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7094 === PERSPECTIVE SHADOW START (exi8mh) ===
design-editor.js:7095 [exi8mh] Text object: G
design-editor.js:7126 [exi8mh] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7159 [exi8mh] Number of shadow steps: 50
design-editor.js:7179 [exi8mh] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [exi8mh] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [exi8mh] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [exi8mh] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [exi8mh] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [exi8mh] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [exi8mh] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [exi8mh] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [exi8mh] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [exi8mh] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [exi8mh] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [exi8mh] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [exi8mh] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [exi8mh] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [exi8mh] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [exi8mh] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [exi8mh] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [exi8mh] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [exi8mh] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [exi8mh] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [exi8mh] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [exi8mh] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [exi8mh] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7094 === PERSPECTIVE SHADOW START (62zsf8) ===
design-editor.js:7095 [62zsf8] Text object: N
design-editor.js:7126 [62zsf8] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:7159 [62zsf8] Number of shadow steps: 50
design-editor.js:7179 [62zsf8] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [62zsf8] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [62zsf8] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [62zsf8] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [62zsf8] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [62zsf8] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [62zsf8] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [62zsf8] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [62zsf8] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [62zsf8] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [62zsf8] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [62zsf8] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [62zsf8] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [62zsf8] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [62zsf8] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [62zsf8] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [62zsf8] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [62zsf8] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [62zsf8] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [62zsf8] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [62zsf8] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [62zsf8] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [62zsf8] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:3526 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for normal text (non-gradient)
design-editor.js:4796 🎨 NORMAL FRONT OUTLINE: Drawing normal text front outline
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:4818 🎨 NORMAL FRONT OUTLINE: Drew normal text front outline
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 12
design-editor.js:8963 === END SHADOW MODE CHANGE DEBUG ===
design-editor.js:10731 📚 State saved to history: Change shadowMode Index: 6 Stack size: 7
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 12, newValue: 15}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 12, newValue: 15, effectiveValue: 15}
design-editor.js:1578 Forcing redraw...
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7094 === PERSPECTIVE SHADOW START (dy7nq4) ===
design-editor.js:7095 [dy7nq4] Text object: D
design-editor.js:7126 [dy7nq4] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7159 [dy7nq4] Number of shadow steps: 50
design-editor.js:7179 [dy7nq4] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [dy7nq4] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [dy7nq4] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [dy7nq4] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [dy7nq4] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [dy7nq4] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [dy7nq4] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [dy7nq4] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [dy7nq4] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [dy7nq4] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [dy7nq4] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [dy7nq4] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [dy7nq4] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [dy7nq4] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [dy7nq4] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [dy7nq4] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [dy7nq4] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [dy7nq4] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [dy7nq4] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [dy7nq4] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [dy7nq4] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [dy7nq4] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [dy7nq4] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7094 === PERSPECTIVE SHADOW START (dju7bi) ===
design-editor.js:7095 [dju7bi] Text object: E
design-editor.js:7126 [dju7bi] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7159 [dju7bi] Number of shadow steps: 50
design-editor.js:7179 [dju7bi] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [dju7bi] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [dju7bi] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [dju7bi] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [dju7bi] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [dju7bi] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [dju7bi] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [dju7bi] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [dju7bi] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [dju7bi] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [dju7bi] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [dju7bi] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [dju7bi] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [dju7bi] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [dju7bi] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [dju7bi] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [dju7bi] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [dju7bi] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [dju7bi] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [dju7bi] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [dju7bi] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [dju7bi] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [dju7bi] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7094 === PERSPECTIVE SHADOW START (mgd8xq) ===
design-editor.js:7095 [mgd8xq] Text object: S
design-editor.js:7126 [mgd8xq] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7159 [mgd8xq] Number of shadow steps: 50
design-editor.js:7179 [mgd8xq] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [mgd8xq] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [mgd8xq] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [mgd8xq] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [mgd8xq] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [mgd8xq] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [mgd8xq] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [mgd8xq] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [mgd8xq] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [mgd8xq] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [mgd8xq] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [mgd8xq] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [mgd8xq] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [mgd8xq] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [mgd8xq] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [mgd8xq] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [mgd8xq] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [mgd8xq] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [mgd8xq] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [mgd8xq] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [mgd8xq] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [mgd8xq] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [mgd8xq] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7094 === PERSPECTIVE SHADOW START (532oz6) ===
design-editor.js:7095 [532oz6] Text object: I
design-editor.js:7126 [532oz6] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7159 [532oz6] Number of shadow steps: 50
design-editor.js:7179 [532oz6] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [532oz6] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [532oz6] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [532oz6] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [532oz6] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [532oz6] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [532oz6] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [532oz6] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [532oz6] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [532oz6] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [532oz6] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [532oz6] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [532oz6] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [532oz6] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [532oz6] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [532oz6] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [532oz6] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [532oz6] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [532oz6] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [532oz6] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [532oz6] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [532oz6] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [532oz6] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7094 === PERSPECTIVE SHADOW START (6dhv6g) ===
design-editor.js:7095 [6dhv6g] Text object: G
design-editor.js:7126 [6dhv6g] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7159 [6dhv6g] Number of shadow steps: 50
design-editor.js:7179 [6dhv6g] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [6dhv6g] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [6dhv6g] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [6dhv6g] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [6dhv6g] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [6dhv6g] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [6dhv6g] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [6dhv6g] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [6dhv6g] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [6dhv6g] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [6dhv6g] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [6dhv6g] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [6dhv6g] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [6dhv6g] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [6dhv6g] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [6dhv6g] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [6dhv6g] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [6dhv6g] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [6dhv6g] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [6dhv6g] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [6dhv6g] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [6dhv6g] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [6dhv6g] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7094 === PERSPECTIVE SHADOW START (3kiqh3) ===
design-editor.js:7095 [3kiqh3] Text object: N
design-editor.js:7126 [3kiqh3] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7159 [3kiqh3] Number of shadow steps: 50
design-editor.js:7179 [3kiqh3] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [3kiqh3] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [3kiqh3] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [3kiqh3] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [3kiqh3] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [3kiqh3] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [3kiqh3] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [3kiqh3] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [3kiqh3] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [3kiqh3] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [3kiqh3] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [3kiqh3] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [3kiqh3] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [3kiqh3] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [3kiqh3] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [3kiqh3] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [3kiqh3] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [3kiqh3] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [3kiqh3] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [3kiqh3] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [3kiqh3] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [3kiqh3] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [3kiqh3] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:3526 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for normal text (non-gradient)
design-editor.js:4796 🎨 NORMAL FRONT OUTLINE: Drawing normal text front outline
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:4818 🎨 NORMAL FRONT OUTLINE: Drew normal text front outline
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 15, newValue: 16}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 15, newValue: 16, effectiveValue: 16}
design-editor.js:1578 Forcing redraw...
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 16
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 16
design-editor.js:7094 === PERSPECTIVE SHADOW START (pq0fha) ===
design-editor.js:7095 [pq0fha] Text object: D
design-editor.js:7126 [pq0fha] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 16
design-editor.js:7159 [pq0fha] Number of shadow steps: 50
design-editor.js:7179 [pq0fha] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [pq0fha] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [pq0fha] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [pq0fha] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [pq0fha] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [pq0fha] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [pq0fha] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [pq0fha] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [pq0fha] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [pq0fha] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [pq0fha] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [pq0fha] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [pq0fha] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [pq0fha] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [pq0fha] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [pq0fha] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [pq0fha] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [pq0fha] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [pq0fha] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [pq0fha] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [pq0fha] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [pq0fha] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [pq0fha] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 16
design-editor.js:7094 === PERSPECTIVE SHADOW START (vrkknh) ===
design-editor.js:7095 [vrkknh] Text object: E
design-editor.js:7126 [vrkknh] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 16
design-editor.js:7159 [vrkknh] Number of shadow steps: 50
design-editor.js:7179 [vrkknh] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [vrkknh] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [vrkknh] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [vrkknh] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [vrkknh] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [vrkknh] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [vrkknh] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [vrkknh] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [vrkknh] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [vrkknh] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [vrkknh] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [vrkknh] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [vrkknh] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [vrkknh] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [vrkknh] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [vrkknh] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [vrkknh] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [vrkknh] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [vrkknh] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [vrkknh] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [vrkknh] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [vrkknh] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [vrkknh] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 16
design-editor.js:7094 === PERSPECTIVE SHADOW START (j9s7wa) ===
design-editor.js:7095 [j9s7wa] Text object: S
design-editor.js:7126 [j9s7wa] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 16
design-editor.js:7159 [j9s7wa] Number of shadow steps: 50
design-editor.js:7179 [j9s7wa] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [j9s7wa] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [j9s7wa] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [j9s7wa] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [j9s7wa] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [j9s7wa] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [j9s7wa] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [j9s7wa] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [j9s7wa] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [j9s7wa] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [j9s7wa] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [j9s7wa] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [j9s7wa] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [j9s7wa] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [j9s7wa] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [j9s7wa] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [j9s7wa] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [j9s7wa] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [j9s7wa] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [j9s7wa] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [j9s7wa] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [j9s7wa] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [j9s7wa] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 16
design-editor.js:7094 === PERSPECTIVE SHADOW START (k2n332) ===
design-editor.js:7095 [k2n332] Text object: I
design-editor.js:7126 [k2n332] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 16
design-editor.js:7159 [k2n332] Number of shadow steps: 50
design-editor.js:7179 [k2n332] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [k2n332] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [k2n332] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [k2n332] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [k2n332] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [k2n332] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [k2n332] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [k2n332] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [k2n332] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [k2n332] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [k2n332] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [k2n332] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [k2n332] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [k2n332] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [k2n332] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [k2n332] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [k2n332] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [k2n332] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [k2n332] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [k2n332] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [k2n332] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [k2n332] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [k2n332] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 16
design-editor.js:7094 === PERSPECTIVE SHADOW START (cqb4lg) ===
design-editor.js:7095 [cqb4lg] Text object: G
design-editor.js:7126 [cqb4lg] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 16
design-editor.js:7159 [cqb4lg] Number of shadow steps: 50
design-editor.js:7179 [cqb4lg] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [cqb4lg] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [cqb4lg] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [cqb4lg] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [cqb4lg] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [cqb4lg] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [cqb4lg] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [cqb4lg] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [cqb4lg] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [cqb4lg] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [cqb4lg] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [cqb4lg] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [cqb4lg] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [cqb4lg] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [cqb4lg] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [cqb4lg] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [cqb4lg] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [cqb4lg] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [cqb4lg] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [cqb4lg] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [cqb4lg] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [cqb4lg] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [cqb4lg] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 16
design-editor.js:7094 === PERSPECTIVE SHADOW START (kjjn2i) ===
design-editor.js:7095 [kjjn2i] Text object: N
design-editor.js:7126 [kjjn2i] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 16
design-editor.js:7159 [kjjn2i] Number of shadow steps: 50
design-editor.js:7179 [kjjn2i] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [kjjn2i] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [kjjn2i] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [kjjn2i] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [kjjn2i] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [kjjn2i] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [kjjn2i] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [kjjn2i] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [kjjn2i] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [kjjn2i] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [kjjn2i] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [kjjn2i] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [kjjn2i] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [kjjn2i] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [kjjn2i] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [kjjn2i] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [kjjn2i] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [kjjn2i] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [kjjn2i] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [kjjn2i] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [kjjn2i] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [kjjn2i] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [kjjn2i] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:3526 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for normal text (non-gradient)
design-editor.js:4796 🎨 NORMAL FRONT OUTLINE: Drawing normal text front outline
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 16
design-editor.js:4818 🎨 NORMAL FRONT OUTLINE: Drew normal text front outline
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 16
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 16
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:1263 === UPDATE OBJECT PROPERTY DEBUG (letterSpacing) ===
design-editor.js:1272 Updating property 'letterSpacing' on object: {id: 0, type: 'text', text: 'DESIGN', oldValue: 16, newValue: 15}
design-editor.js:1335 Property 'letterSpacing' updated: {oldValue: 16, newValue: 15, effectiveValue: 15}
design-editor.js:1578 Forcing redraw...
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7094 === PERSPECTIVE SHADOW START (p4og39) ===
design-editor.js:7095 [p4og39] Text object: D
design-editor.js:7126 [p4og39] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7159 [p4og39] Number of shadow steps: 50
design-editor.js:7179 [p4og39] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [p4og39] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [p4og39] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [p4og39] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [p4og39] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [p4og39] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [p4og39] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [p4og39] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [p4og39] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [p4og39] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [p4og39] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [p4og39] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [p4og39] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [p4og39] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [p4og39] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [p4og39] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [p4og39] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [p4og39] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [p4og39] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [p4og39] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [p4og39] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [p4og39] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [p4og39] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7094 === PERSPECTIVE SHADOW START (zd9x5j) ===
design-editor.js:7095 [zd9x5j] Text object: E
design-editor.js:7126 [zd9x5j] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7159 [zd9x5j] Number of shadow steps: 50
design-editor.js:7179 [zd9x5j] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [zd9x5j] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [zd9x5j] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [zd9x5j] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [zd9x5j] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [zd9x5j] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [zd9x5j] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [zd9x5j] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [zd9x5j] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [zd9x5j] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [zd9x5j] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [zd9x5j] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [zd9x5j] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [zd9x5j] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [zd9x5j] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [zd9x5j] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [zd9x5j] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [zd9x5j] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [zd9x5j] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [zd9x5j] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [zd9x5j] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [zd9x5j] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [zd9x5j] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7094 === PERSPECTIVE SHADOW START (9ufrpc) ===
design-editor.js:7095 [9ufrpc] Text object: S
design-editor.js:7126 [9ufrpc] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7159 [9ufrpc] Number of shadow steps: 50
design-editor.js:7179 [9ufrpc] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [9ufrpc] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [9ufrpc] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [9ufrpc] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [9ufrpc] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [9ufrpc] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [9ufrpc] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [9ufrpc] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [9ufrpc] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [9ufrpc] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [9ufrpc] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [9ufrpc] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [9ufrpc] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [9ufrpc] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [9ufrpc] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [9ufrpc] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [9ufrpc] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [9ufrpc] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [9ufrpc] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [9ufrpc] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [9ufrpc] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [9ufrpc] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [9ufrpc] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7094 === PERSPECTIVE SHADOW START (1mjtq6) ===
design-editor.js:7095 [1mjtq6] Text object: I
design-editor.js:7126 [1mjtq6] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7159 [1mjtq6] Number of shadow steps: 50
design-editor.js:7179 [1mjtq6] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [1mjtq6] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [1mjtq6] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [1mjtq6] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [1mjtq6] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [1mjtq6] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [1mjtq6] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [1mjtq6] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [1mjtq6] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [1mjtq6] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [1mjtq6] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [1mjtq6] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [1mjtq6] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [1mjtq6] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [1mjtq6] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [1mjtq6] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [1mjtq6] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [1mjtq6] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [1mjtq6] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [1mjtq6] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [1mjtq6] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [1mjtq6] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [1mjtq6] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7094 === PERSPECTIVE SHADOW START (unel70) ===
design-editor.js:7095 [unel70] Text object: G
design-editor.js:7126 [unel70] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7159 [unel70] Number of shadow steps: 50
design-editor.js:7179 [unel70] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [unel70] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [unel70] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [unel70] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [unel70] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [unel70] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [unel70] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [unel70] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [unel70] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [unel70] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [unel70] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [unel70] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [unel70] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [unel70] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [unel70] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [unel70] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [unel70] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [unel70] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [unel70] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [unel70] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [unel70] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [unel70] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [unel70] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:676 🎨 Set fillStyle to solid color for letter-by-letter rendering: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7094 === PERSPECTIVE SHADOW START (2xizeo) ===
design-editor.js:7095 [2xizeo] Text object: N
design-editor.js:7126 [2xizeo] Perspective Shadow properties: {color: '#333333', opacity: 1, offset: 6, angleDeg: 105, blur: 2, …}
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:7159 [2xizeo] Number of shadow steps: 50
design-editor.js:7179 [2xizeo] Shadow step 1/50: distanceRatio=0.02, scaleProgress=0.810
design-editor.js:7193 [2xizeo] Shadow position: (1014.9, 1058.1), scale: 0.810
design-editor.js:7179 [2xizeo] Shadow step 5/50: distanceRatio=0.10, scaleProgress=0.825
design-editor.js:7193 [2xizeo] Shadow position: (1015.6, 1055.3), scale: 0.825
design-editor.js:7179 [2xizeo] Shadow step 10/50: distanceRatio=0.20, scaleProgress=0.845
design-editor.js:7193 [2xizeo] Shadow position: (1016.5, 1051.8), scale: 0.845
design-editor.js:7179 [2xizeo] Shadow step 15/50: distanceRatio=0.30, scaleProgress=0.864
design-editor.js:7193 [2xizeo] Shadow position: (1017.5, 1048.3), scale: 0.864
design-editor.js:7179 [2xizeo] Shadow step 20/50: distanceRatio=0.40, scaleProgress=0.884
design-editor.js:7193 [2xizeo] Shadow position: (1018.4, 1044.9), scale: 0.884
design-editor.js:7179 [2xizeo] Shadow step 25/50: distanceRatio=0.50, scaleProgress=0.903
design-editor.js:7193 [2xizeo] Shadow position: (1019.3, 1041.4), scale: 0.903
design-editor.js:7179 [2xizeo] Shadow step 30/50: distanceRatio=0.60, scaleProgress=0.922
design-editor.js:7193 [2xizeo] Shadow position: (1020.3, 1037.9), scale: 0.922
design-editor.js:7179 [2xizeo] Shadow step 35/50: distanceRatio=0.70, scaleProgress=0.942
design-editor.js:7193 [2xizeo] Shadow position: (1021.2, 1034.4), scale: 0.942
design-editor.js:7179 [2xizeo] Shadow step 40/50: distanceRatio=0.80, scaleProgress=0.961
design-editor.js:7193 [2xizeo] Shadow position: (1022.1, 1031.0), scale: 0.961
design-editor.js:7179 [2xizeo] Shadow step 45/50: distanceRatio=0.90, scaleProgress=0.981
design-editor.js:7193 [2xizeo] Shadow position: (1023.1, 1027.5), scale: 0.981
design-editor.js:7179 [2xizeo] Shadow step 50/50: distanceRatio=1.00, scaleProgress=1.000
design-editor.js:7193 [2xizeo] Shadow position: (1024.0, 1024.0), scale: 1.000
design-editor.js:7226 [2xizeo] === PERSPECTIVE SHADOW END ===
design-editor.js:3361 🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only
design-editor.js:3526 🔍 RENDER ORDER: Drawing perspective shadow front outline on top for normal text (non-gradient)
design-editor.js:4796 🎨 NORMAL FRONT OUTLINE: Drawing normal text front outline
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:4818 🎨 NORMAL FRONT OUTLINE: Drew normal text front outline
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:667 🎨 setTextContextOn - textObj.gradient: null
design-editor.js:668 🎨 setTextContextOn - textObj.color: #3b82f6
design-editor.js:680 🎨 Set default fillStyle for normal text: #3b82f6
design-editor.js:695 Setting text context with font: 200px Poppins letter spacing: 15
design-editor.js:1580 Property 'letterSpacing' update complete
design-editor.js:1655 === END UPDATE OBJECT PROPERTY DEBUG ===
design-editor.js:10731 📚 State saved to history: Change letterSpacing Index: 7 Stack size: 8